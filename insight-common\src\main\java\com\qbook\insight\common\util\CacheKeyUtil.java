package com.qbook.insight.common.util;

import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.MD5;

import java.util.Arrays;
import java.util.stream.Collectors;

/**
 * 缓存键生成工具类
 * 提供统一的缓存键命名规范和生成方法
 * 
 * <AUTHOR>
 */
public class CacheKeyUtil {

    /**
     * 缓存键分隔符
     */
    private static final String SEPARATOR = ":";
    
    /**
     * 数据库查询缓存前缀
     */
    private static final String DB_QUERY_PREFIX = "db_query";
    
    /**
     * 配置缓存前缀
     */
    private static final String CONFIG_PREFIX = "config";

    /**
     * 生成数据库查询缓存键
     * 格式: db_query:表名:查询条件哈希值
     * 
     * @param tableName 表名
     * @param queryParams 查询参数（用于生成唯一标识）
     * @return 缓存键
     */
    public static String buildDbQueryKey(String tableName, Object... queryParams) {
        String paramsHash = generateParamsHash(queryParams);
        return String.join(SEPARATOR, DB_QUERY_PREFIX, tableName, paramsHash);
    }

    /**
     * 生成配置缓存键
     * 格式: config:配置类型:配置标识
     * 
     * @param configType 配置类型
     * @param configId 配置标识
     * @return 缓存键
     */
    public static String buildConfigKey(String configType, String configId) {
        return String.join(SEPARATOR, CONFIG_PREFIX, configType, configId);
    }

    /**
     * 生成通用缓存键
     * 格式: 前缀:业务标识1:业务标识2:...
     * 
     * @param prefix 前缀
     * @param identifiers 业务标识符
     * @return 缓存键
     */
    public static String buildKey(String prefix, String... identifiers) {
        if (StrUtil.isBlank(prefix)) {
            throw new IllegalArgumentException("Cache key prefix cannot be blank");
        }
        
        if (identifiers == null || identifiers.length == 0) {
            return prefix;
        }
        
        // 过滤空值并连接
        String[] parts = new String[identifiers.length + 1];
        parts[0] = prefix;
        System.arraycopy(identifiers, 0, parts, 1, identifiers.length);
        
        return Arrays.stream(parts)
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.joining(SEPARATOR));
    }

    /**
     * 生成指标配置缓存键
     * 
     * @param status 状态参数
     * @return 缓存键
     */
    public static String buildIndicatorConfigKey(Integer status) {
        if (status != null) {
            return buildDbQueryKey("indicator_config", "status", status);
        } else {
            return buildDbQueryKey("indicator_config", "all");
        }
    }

    /**
     * 生成指标配置列表缓存键（带多个查询条件）
     * 
     * @param status 状态
     * @param indicatorName 指标名称
     * @param indicatorCode 指标编码
     * @return 缓存键
     */
    public static String buildIndicatorConfigListKey(Integer status, String indicatorName, String indicatorCode) {
        return buildDbQueryKey("indicator_config", 
                "status", status, 
                "name", indicatorName, 
                "code", indicatorCode);
    }

    /**
     * 为查询参数生成哈希值
     * 用于确保相同查询条件生成相同的缓存键
     * 
     * @param params 查询参数
     * @return 参数哈希值
     */
    private static String generateParamsHash(Object... params) {
        if (params == null || params.length == 0) {
            return "empty";
        }
        
        String paramsStr = Arrays.stream(params)
                .map(param -> param == null ? "null" : param.toString())
                .collect(Collectors.joining("_"));
        
        // 使用MD5生成短哈希，避免键过长
        return MD5.create().digestHex(paramsStr).substring(0, 8);
    }

    /**
     * 生成缓存键模式（用于批量删除）
     * 
     * @param prefix 前缀
     * @param pattern 模式（支持*通配符）
     * @return 缓存键模式
     */
    public static String buildKeyPattern(String prefix, String pattern) {
        return prefix + SEPARATOR + pattern;
    }

    /**
     * 获取指标配置相关的所有缓存键模式
     * 用于缓存失效时批量清理
     * 
     * @return 缓存键模式
     */
    public static String getIndicatorConfigKeyPattern() {
        return buildKeyPattern(DB_QUERY_PREFIX, "indicator_config*");
    }
}
