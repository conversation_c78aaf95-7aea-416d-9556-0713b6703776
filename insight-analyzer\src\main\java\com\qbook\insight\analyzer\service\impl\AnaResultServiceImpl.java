package com.qbook.insight.analyzer.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.qbook.insight.analyzer.service.AnaResultService;
import com.qbook.insight.analyzer.util.UniversalResultBuilder;
import com.qbook.insight.common.domain.report.result.BasicInfo;
import com.qbook.insight.common.domain.report.result.LegalPerson;
import com.qbook.insight.common.domain.report.result.Result;
import com.qbook.insight.common.entity.IndicatorConfig;
import com.qbook.insight.common.entity.Report;
import com.qbook.insight.common.entity.ReportResult;
import com.qbook.insight.common.mapper.CorpMapper;
import com.qbook.insight.common.mapper.IndicatorConfigMapper;
import com.qbook.insight.common.mapper.ReportResultMapper;
import com.qbook.insight.common.util.IndicatorCalculator;
import com.qbook.insight.common.util.RedisUtil;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 报告结果分析实现
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class AnaResultServiceImpl implements AnaResultService {

  /** 指标配置缓存键前缀 */
  private static final String INDICATOR_CONFIG_CACHE_KEY = "indicator_config:enabled";

  /** 缓存过期时间：3分钟 */
  private static final long CACHE_EXPIRE_MINUTES = 3;

  @Resource private IndicatorConfigMapper indicatorConfigMapper;
  @Resource private IndicatorCalculator indicatorCalculator;
  @Resource private ReportResultMapper reportResultMapper;
  @Resource private CorpMapper corpMapper;
  @Resource private RedisUtil redisUtil;

  @Override
  @Transactional
  public void anaReportResult(Report report) {
    List<IndicatorConfig> configList = getEnabledIndicatorConfigs();

    Map<String, Object> map = new HashMap<>();
    map.put("tax_id", "91330681MA2D7C9R1B");

    Map<String, Object> resultMap =
        configList.stream()
            .map(config -> indicatorCalculator.calculate(config, map))
            .flatMap(m -> m.entrySet().stream())
            .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
    log.info("报告结果:{}", resultMap);

    Result result = UniversalResultBuilder.buildResult(resultMap);

    Map<String, Object> corpInfo = corpMapper.selectCorpInfoById(report.getCorpId());
    BasicInfo basicInfo = BeanUtil.toBean(corpInfo, BasicInfo.class);

    LegalPerson legalPerson = BeanUtil.toBean(corpInfo, LegalPerson.class);
    basicInfo.setLegalPerson(legalPerson);

    result.setBasicInfo(basicInfo);

    ReportResult reportResult =
        new ReportResult()
            .setReportId(report.getId())
            .setUserId(report.getUserId())
            .setResult(JSONUtil.toJsonStr(result));

    log.info("报告结果生成完成: {}", JSONUtil.toJsonStr(result));
    log.info("报告结果生成完成: {}", reportResult);
    reportResultMapper.insert(reportResult);
  }

  /**
   * 获取启用状态的指标配置列表（带Redis缓存）
   *
   * @return 启用状态的指标配置列表
   */
  private List<IndicatorConfig> getEnabledIndicatorConfigs() {
    try {
      // 1. 先从Redis缓存中获取
      String cachedData = redisUtil.get(INDICATOR_CONFIG_CACHE_KEY);
      if (cachedData != null) {
        log.debug("从Redis缓存中获取指标配置数据");
        return JSONUtil.toList(cachedData, IndicatorConfig.class);
      }

      // 2. 缓存中没有，从数据库查询
      log.debug("缓存中没有数据，从数据库查询指标配置");
      List<IndicatorConfig> configList = queryEnabledIndicatorConfigsFromDb();

      // 3. 将查询结果存入Redis缓存，设置3分钟过期时间
      if (!configList.isEmpty()) {
        String jsonData = JSONUtil.toJsonStr(configList);
        redisUtil.set(INDICATOR_CONFIG_CACHE_KEY, jsonData, CACHE_EXPIRE_MINUTES, TimeUnit.MINUTES);
        log.debug("指标配置数据已存入Redis缓存，过期时间：{}分钟", CACHE_EXPIRE_MINUTES);
      }

      return configList;
    } catch (Exception e) {
      log.error("获取指标配置时发生异常，降级到直接查询数据库", e);
      // 发生异常时降级到直接查询数据库
      return queryEnabledIndicatorConfigsFromDb();
    }
  }

  /**
   * 从数据库查询启用状态的指标配置
   *
   * @return 启用状态的指标配置列表
   */
  private List<IndicatorConfig> queryEnabledIndicatorConfigsFromDb() {
    LambdaQueryWrapper<IndicatorConfig> wrapper = new LambdaQueryWrapper<>();
    wrapper.eq(IndicatorConfig::getStatus, 1);
    return indicatorConfigMapper.selectList(wrapper);
  }
}
