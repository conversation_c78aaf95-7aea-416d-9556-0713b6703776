package com.qbook.insight.analyzer.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.qbook.insight.analyzer.service.AnaResultService;
import com.qbook.insight.analyzer.util.UniversalResultBuilder;
import com.qbook.insight.common.domain.report.result.BasicInfo;
import com.qbook.insight.common.domain.report.result.LegalPerson;
import com.qbook.insight.common.domain.report.result.Result;
import com.qbook.insight.common.entity.IndicatorConfig;
import com.qbook.insight.common.entity.Report;
import com.qbook.insight.common.entity.ReportResult;
import com.qbook.insight.common.mapper.CorpMapper;
import com.qbook.insight.common.mapper.IndicatorConfigMapper;
import com.qbook.insight.common.mapper.ReportResultMapper;
import com.qbook.insight.common.util.IndicatorCalculator;
import java.util.*;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 报告结果分析实现
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class AnaResultServiceImpl implements AnaResultService {

  @Resource private IndicatorConfigMapper indicatorConfigMapper;
  @Resource private IndicatorCalculator indicatorCalculator;
  @Resource private ReportResultMapper reportResultMapper;
  @Resource private CorpMapper corpMapper;

  @Override
  @Transactional
  public void anaReportResult(Report report) {
    LambdaQueryWrapper<IndicatorConfig> wrapper = new LambdaQueryWrapper<>();
    wrapper.eq(IndicatorConfig::getStatus, 1);
    List<IndicatorConfig> configList = indicatorConfigMapper.selectList(wrapper);

    Map<String, Object> map = new HashMap<>();
    map.put("tax_id", "91330681MA2D7C9R1B");

    Map<String, Object> resultMap =
        configList.stream()
            .map(config -> indicatorCalculator.calculate(config, map))
            .flatMap(m -> m.entrySet().stream())
            .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
    log.info("报告结果:{}", resultMap);

    Result result = UniversalResultBuilder.buildResult(resultMap);

    Map<String, Object> corpInfo = corpMapper.selectCorpInfoById(report.getCorpId());
    BasicInfo basicInfo = BeanUtil.toBean(corpInfo, BasicInfo.class);

    LegalPerson legalPerson = BeanUtil.toBean(corpInfo, LegalPerson.class);
    basicInfo.setLegalPerson(legalPerson);

    result.setBasicInfo(basicInfo);

    ReportResult reportResult =
        new ReportResult()
            .setReportId(report.getId())
            .setUserId(report.getUserId())
            .setResult(JSONUtil.toJsonStr(result));

    log.info("报告结果生成完成: {}", JSONUtil.toJsonStr(result));
    log.info("报告结果生成完成: {}", reportResult);
    reportResultMapper.insert(reportResult);
  }
}
