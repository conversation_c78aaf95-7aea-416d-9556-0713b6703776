package com.qbook.insight.common.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.qbook.insight.common.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

/**
 * 通用Redis缓存服务
 * 提供类型安全的缓存操作，支持自动序列化/反序列化
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
@ConditionalOnProperty(name = "component.redis", havingValue = "true")
public class CacheService {

    @Resource
    private RedisUtil redisUtil;
    
    @Resource
    private ObjectMapper objectMapper;

    /**
     * 获取缓存数据，如果不存在则执行数据加载函数并缓存结果
     * 
     * @param key 缓存键
     * @param typeReference 返回类型引用
     * @param dataLoader 数据加载函数
     * @param timeout 过期时间
     * @param timeUnit 时间单位
     * @param <T> 返回类型
     * @return 缓存或加载的数据
     */
    public <T> T getOrLoad(String key, TypeReference<T> typeReference, 
                          Supplier<T> dataLoader, long timeout, TimeUnit timeUnit) {
        try {
            // 尝试从缓存获取
            String cachedValue = redisUtil.get(key);
            if (cachedValue != null) {
                log.debug("Cache hit for key: {}", key);
                return objectMapper.readValue(cachedValue, typeReference);
            }
            
            // 缓存未命中，执行数据加载
            log.debug("Cache miss for key: {}, loading data...", key);
            T data = dataLoader.get();
            
            if (data != null) {
                // 将数据序列化并存入缓存
                String jsonValue = objectMapper.writeValueAsString(data);
                redisUtil.set(key, jsonValue, timeout, timeUnit);
                log.debug("Data cached for key: {}", key);
            }
            
            return data;
        } catch (JsonProcessingException e) {
            log.error("JSON processing error for cache key: {}", key, e);
            // 发生序列化错误时，直接返回数据加载结果，不缓存
            return dataLoader.get();
        } catch (Exception e) {
            log.error("Cache operation error for key: {}", key, e);
            // 发生其他错误时，直接返回数据加载结果
            return dataLoader.get();
        }
    }

    /**
     * 获取缓存数据，如果不存在则执行数据加载函数并缓存结果（使用Class类型）
     * 
     * @param key 缓存键
     * @param clazz 返回类型Class
     * @param dataLoader 数据加载函数
     * @param timeout 过期时间
     * @param timeUnit 时间单位
     * @param <T> 返回类型
     * @return 缓存或加载的数据
     */
    public <T> T getOrLoad(String key, Class<T> clazz, 
                          Supplier<T> dataLoader, long timeout, TimeUnit timeUnit) {
        try {
            // 尝试从缓存获取
            String cachedValue = redisUtil.get(key);
            if (cachedValue != null) {
                log.debug("Cache hit for key: {}", key);
                return objectMapper.readValue(cachedValue, clazz);
            }
            
            // 缓存未命中，执行数据加载
            log.debug("Cache miss for key: {}, loading data...", key);
            T data = dataLoader.get();
            
            if (data != null) {
                // 将数据序列化并存入缓存
                String jsonValue = objectMapper.writeValueAsString(data);
                redisUtil.set(key, jsonValue, timeout, timeUnit);
                log.debug("Data cached for key: {}", key);
            }
            
            return data;
        } catch (JsonProcessingException e) {
            log.error("JSON processing error for cache key: {}", key, e);
            // 发生序列化错误时，直接返回数据加载结果，不缓存
            return dataLoader.get();
        } catch (Exception e) {
            log.error("Cache operation error for key: {}", key, e);
            // 发生其他错误时，直接返回数据加载结果
            return dataLoader.get();
        }
    }

    /**
     * 直接设置缓存
     * 
     * @param key 缓存键
     * @param value 缓存值
     * @param timeout 过期时间
     * @param timeUnit 时间单位
     * @param <T> 值类型
     */
    public <T> void set(String key, T value, long timeout, TimeUnit timeUnit) {
        try {
            String jsonValue = objectMapper.writeValueAsString(value);
            redisUtil.set(key, jsonValue, timeout, timeUnit);
            log.debug("Data set to cache for key: {}", key);
        } catch (JsonProcessingException e) {
            log.error("Failed to serialize and cache data for key: {}", key, e);
        }
    }

    /**
     * 获取缓存数据
     * 
     * @param key 缓存键
     * @param typeReference 返回类型引用
     * @param <T> 返回类型
     * @return 缓存数据，如果不存在或反序列化失败则返回null
     */
    public <T> T get(String key, TypeReference<T> typeReference) {
        try {
            String cachedValue = redisUtil.get(key);
            if (cachedValue != null) {
                return objectMapper.readValue(cachedValue, typeReference);
            }
        } catch (JsonProcessingException e) {
            log.error("Failed to deserialize cached data for key: {}", key, e);
        }
        return null;
    }

    /**
     * 获取缓存数据（使用Class类型）
     * 
     * @param key 缓存键
     * @param clazz 返回类型Class
     * @param <T> 返回类型
     * @return 缓存数据，如果不存在或反序列化失败则返回null
     */
    public <T> T get(String key, Class<T> clazz) {
        try {
            String cachedValue = redisUtil.get(key);
            if (cachedValue != null) {
                return objectMapper.readValue(cachedValue, clazz);
            }
        } catch (JsonProcessingException e) {
            log.error("Failed to deserialize cached data for key: {}", key, e);
        }
        return null;
    }

    /**
     * 删除缓存
     * 
     * @param key 缓存键
     */
    public void delete(String key) {
        redisUtil.delete(key);
        log.debug("Cache deleted for key: {}", key);
    }

    /**
     * 检查缓存是否存在
     * 
     * @param key 缓存键
     * @return 是否存在
     */
    public boolean exists(String key) {
        return redisUtil.exists(key);
    }
}
