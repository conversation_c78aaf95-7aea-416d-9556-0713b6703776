package com.qbook.insight.common.util;

import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.function.BiConsumer;
import java.util.function.Consumer;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.redis.core.*;
import org.springframework.stereotype.Component;

/**
 * Redis工具类
 *
 * <AUTHOR>
 */
@Component
@ConditionalOnProperty(name = "component.redis", havingValue = "true")
public class RedisUtil {

  private final RedisTemplate<String, String> redisTemplate;

  RedisUtil(RedisTemplate<String, String> redisTemplate) {
    this.redisTemplate = redisTemplate;
  }

  public String get(String key) {
    return redisTemplate.opsForValue().get(key);
  }

  public void set(String key, String value) {
    redisTemplate.opsForValue().set(key, value);
  }

  public void set(String key, String value, long timeout, TimeUnit unit) {
    redisTemplate.opsForValue().set(key, value, timeout, unit);
  }

  public Object blpop(String key, long timeout, TimeUnit unit) {
    return redisTemplate.opsForList().leftPop(key, timeout, unit);
  }

  public Long rpush(String key, String value) {
    return redisTemplate.opsForList().rightPush(key, value);
  }

  public void hset(String key, String field, String value) {
    redisTemplate.opsForHash().put(key, field, value);
  }

  public String hget(String key, String field) {
    return (String) redisTemplate.opsForHash().get(key, field);
  }

  public Map<Object, Object> hgetAll(String key) {
    return redisTemplate.opsForHash().entries(key);
  }

  public void hsetAll(String key, Map<String, String> map) {
    redisTemplate.opsForHash().putAll(key, map);
  }

  public boolean hsetnx(String key, String field, String value) {
    return redisTemplate.opsForHash().putIfAbsent(key, field, value);
  }

  public Long hdel(String key, String... field) {
    return redisTemplate.opsForHash().delete(key, (Object[]) field);
  }

  public void hscan(String key, String pattern, BiConsumer<String, String> consumer) {
    ScanOptions options =
        (pattern == null || pattern.isEmpty())
            ? ScanOptions.scanOptions().build()
            : ScanOptions.scanOptions().match(pattern).build();
    HashOperations<String, String, String> ops = redisTemplate.opsForHash();
    try (Cursor<Map.Entry<String, String>> cursor = ops.scan(key, options)) {
      while (cursor.hasNext()) {
        Map.Entry<String, String> entry = cursor.next();
        consumer.accept(entry.getKey(), entry.getValue());
      }
    }
  }

  public Long sadd(String key, String... members) {
    return redisTemplate.opsForSet().add(key, members);
  }

  public void sscan(String key, Consumer<String> consumer) {
    ScanOptions options = ScanOptions.scanOptions().build();
    try (Cursor<String> cursor = redisTemplate.opsForSet().scan(key, options)) {
      while (cursor.hasNext()) {
        String member = cursor.next();
        consumer.accept(member);
      }
    }
  }

  public Boolean delete(String key) {
    return redisTemplate.delete(key);
  }

  public Boolean exists(String key) {
    return redisTemplate.hasKey(key);
  }
}
